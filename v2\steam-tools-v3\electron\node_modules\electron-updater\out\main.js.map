{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../src/main.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,uCAAmD;AACnD,6BAA4B;AAI5B,6CAA2C;AAAlC,0GAAA,WAAW,OAAA;AACpB,2CAAqD;AAA5C,wGAAA,UAAU,OAAA;AAAE,wGAAA,UAAU,OAAA;AAC/B,iDAA+C;AAAtC,oGAAA,QAAQ,OAAA;AACjB,qDAAmD;AAA1C,kHAAA,eAAe,OAAA;AACxB,2CAAyC;AAAhC,wGAAA,UAAU,OAAA;AACnB,iDAA+C;AAAtC,8GAAA,aAAa,OAAA;AACtB,2CAAyC;AAAhC,wGAAA,UAAU,OAAA;AACnB,2CAAyC;AAAhC,wGAAA,UAAU,OAAA;AACnB,6CAA2C;AAAlC,0GAAA,WAAW,OAAA;AAEpB,0CAAuB;AAEvB,oDAAoD;AACpD,IAAI,YAAiB,CAAA;AAKrB,SAAS,iBAAiB;IACxB,+CAA+C;IAC/C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,EAAE,CAAA;IAC7D,CAAC;SAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACzC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,EAAE,CAAA;IAC3D,CAAC;SAAM,CAAC;QACN,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,eAAe,CAAC,EAAE,CAAA;QACnE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;YACjE,IAAI,CAAC,IAAA,qBAAU,EAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1B,OAAO,YAAY,CAAA;YACrB,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAA;YAC9E,MAAM,QAAQ,GAAG,IAAA,uBAAY,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAA;YACzD,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAA;YAC7C,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,KAAK;oBACR,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,EAAE,CAAA;oBACzD,MAAK;gBACP,KAAK,KAAK;oBACR,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,EAAE,CAAA;oBACzD,MAAK;gBACP,KAAK,QAAQ;oBACX,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,EAAE,CAAA;oBAC/D,MAAK;gBACP;oBACE,MAAK;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CACV,2JAA2J,EAC3J,KAAK,CAAC,OAAO,CACd,CAAA;QACH,CAAC;IACH,CAAC;IACD,OAAO,YAAY,CAAA;AACrB,CAAC;AAED,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,EAAE;IAC5C,UAAU,EAAE,IAAI;IAChB,GAAG,EAAE,GAAG,EAAE;QACR,OAAO,YAAY,IAAI,iBAAiB,EAAE,CAAA;IAC5C,CAAC;CACF,CAAC,CAAA", "sourcesContent": ["import { existsSync, readFileSync } from \"fs-extra\"\nimport * as path from \"path\"\nimport { AppUpdater } from \"./AppUpdater\"\nimport { UpdateInfo } from \"builder-util-runtime\"\n\nexport { BaseUpdater } from \"./BaseUpdater\"\nexport { AppUpdater, NoOpLogger } from \"./AppUpdater\"\nexport { Provider } from \"./providers/Provider\"\nexport { AppImageUpdater } from \"./AppImageUpdater\"\nexport { DebUpdater } from \"./DebUpdater\"\nexport { PacmanUpdater } from \"./PacmanUpdater\"\nexport { RpmUpdater } from \"./RpmUpdater\"\nexport { MacUpdater } from \"./MacUpdater\"\nexport { NsisUpdater } from \"./NsisUpdater\"\n\nexport * from \"./types\"\n\n// autoUpdater to mimic electron bundled autoUpdater\nlet _autoUpdater: any\n\n// required for jsdoc\nexport declare const autoUpdater: AppUpdater\n\nfunction doLoadAutoUpdater(): AppUpdater {\n  // tslint:disable:prefer-conditional-expression\n  if (process.platform === \"win32\") {\n    _autoUpdater = new (require(\"./NsisUpdater\").NsisUpdater)()\n  } else if (process.platform === \"darwin\") {\n    _autoUpdater = new (require(\"./MacUpdater\").MacUpdater)()\n  } else {\n    _autoUpdater = new (require(\"./AppImageUpdater\").AppImageUpdater)()\n    try {\n      const identity = path.join(process.resourcesPath, \"package-type\")\n      if (!existsSync(identity)) {\n        return _autoUpdater\n      }\n      console.info(\"Checking for beta autoupdate feature for deb/rpm distributions\")\n      const fileType = readFileSync(identity).toString().trim()\n      console.info(\"Found package-type:\", fileType)\n      switch (fileType) {\n        case \"deb\":\n          _autoUpdater = new (require(\"./DebUpdater\").DebUpdater)()\n          break\n        case \"rpm\":\n          _autoUpdater = new (require(\"./RpmUpdater\").RpmUpdater)()\n          break\n        case \"pacman\":\n          _autoUpdater = new (require(\"./PacmanUpdater\").PacmanUpdater)()\n          break\n        default:\n          break\n      }\n    } catch (error: any) {\n      console.warn(\n        \"Unable to detect 'package-type' for autoUpdater (beta rpm/deb support). If you'd like to expand support, please consider contributing to electron-builder\",\n        error.message\n      )\n    }\n  }\n  return _autoUpdater\n}\n\nObject.defineProperty(exports, \"autoUpdater\", {\n  enumerable: true,\n  get: () => {\n    return _autoUpdater || doLoadAutoUpdater()\n  },\n})\n\n/**\n * return null if verify signature succeed\n * return error message if verify signature failed\n */\nexport type VerifyUpdateCodeSignature = (publisherName: string[], path: string) => Promise<string | null>\n\nexport type VerifyUpdateSupport = (updateInfo: UpdateInfo) => boolean | Promise<boolean>\n"]}