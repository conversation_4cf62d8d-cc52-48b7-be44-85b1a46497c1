{"version": 3, "file": "electronHttpExecutor.js", "sourceRoot": "", "sources": ["../src/electronHttpExecutor.ts"], "names": [], "mappings": ";;;AASA,sCAIC;AAbD,+DAAkH;AAOrG,QAAA,gBAAgB,GAAG,kBAAkB,CAAA;AAElD,SAAgB,aAAa;IAC3B,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,wBAAgB,EAAE;QACjE,KAAK,EAAE,KAAK;KACb,CAAC,CAAA;AACJ,CAAC;AAED,MAAa,oBAAqB,SAAQ,mCAAoC;IAG5E,YAA6B,kBAA0E;QACrG,KAAK,EAAE,CAAA;QADoB,uBAAkB,GAAlB,kBAAkB,CAAwD;QAF/F,kBAAa,GAAmB,IAAI,CAAA;IAI5C,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAQ,EAAE,WAAmB,EAAE,OAAwB;QACpE,OAAO,MAAM,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YACzF,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,SAAS;gBACrC,QAAQ,EAAE,QAAQ;aACnB,CAAA;YACD,IAAA,0CAAmB,EAAC,GAAG,EAAE,cAAc,CAAC,CAAA;YACxC,IAAA,8CAAuB,EAAC,cAAc,CAAC,CAAA;YACvC,IAAI,CAAC,UAAU,CACb,cAAc,EACd;gBACE,WAAW;gBACX,OAAO;gBACP,QAAQ;gBACR,QAAQ,EAAE,KAAK,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;wBAClB,OAAO,CAAC,WAAW,CAAC,CAAA;oBACtB,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAA;oBACf,CAAC;gBACH,CAAC;gBACD,eAAe,EAAE,IAAI;aACtB,EACD,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,aAAa,CAAC,OAAY,EAAE,QAAiC;QAC3D,wHAAwH;QACxH,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC5C,mCAAmC;YACnC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAA;YACnC,2FAA2F;YAC3F,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAA;QAC7B,CAAC;QAED,uFAAuF;QACvF,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,aAAa,EAAE,CAAA;QACtC,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;YAC9C,GAAG,OAAO;YACV,OAAO,EAAE,IAAI,CAAC,aAAa;SAC5B,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;QAChC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC;YACpC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAC9C,CAAC;QACD,OAAO,OAAO,CAAA;IAChB,CAAC;IAES,mBAAmB,CAC3B,OAAsB,EACtB,OAAuB,EACvB,MAA8B,EAC9B,aAAqB,EACrB,OAA0C;QAE1C,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,UAAkB,EAAE,MAAc,EAAE,WAAmB,EAAE,EAAE;YACjF,iEAAiE;YACjE,oDAAoD;YACpD,OAAO,CAAC,KAAK,EAAE,CAAA;YAEf,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAA;YACvC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,mCAAY,CAAC,yBAAyB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAA;YACvE,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AA/ED,oDA+EC", "sourcesContent": ["import { DownloadOptions, HttpExecutor, configureRequestOptions, configureRequestUrl } from \"builder-util-runtime\"\nimport type { AuthInfo } from \"electron\"\nimport { RequestOptions } from \"http\"\nimport Session = Electron.Session\nimport ClientRequest = Electron.ClientRequest\n\nexport type LoginCallback = (username: string, password: string) => void\nexport const NET_SESSION_NAME = \"electron-updater\"\n\nexport function getNetSession(): Session {\n  return require(\"electron\").session.fromPartition(NET_SESSION_NAME, {\n    cache: false,\n  })\n}\n\nexport class ElectronHttpExecutor extends HttpExecutor<Electron.ClientRequest> {\n  private cachedSession: Session | null = null\n\n  constructor(private readonly proxyLoginCallback?: (authInfo: AuthInfo, callback: LoginCallback) => void) {\n    super()\n  }\n\n  async download(url: URL, destination: string, options: DownloadOptions): Promise<string> {\n    return await options.cancellationToken.createPromise<string>((resolve, reject, onCancel) => {\n      const requestOptions = {\n        headers: options.headers || undefined,\n        redirect: \"manual\",\n      }\n      configureRequestUrl(url, requestOptions)\n      configureRequestOptions(requestOptions)\n      this.doDownload(\n        requestOptions,\n        {\n          destination,\n          options,\n          onCancel,\n          callback: error => {\n            if (error == null) {\n              resolve(destination)\n            } else {\n              reject(error)\n            }\n          },\n          responseHandler: null,\n        },\n        0\n      )\n    })\n  }\n\n  createRequest(options: any, callback: (response: any) => void): Electron.ClientRequest {\n    // fix (node 7+) for making electron updater work when using AWS private buckets, check if headers contain Host property\n    if (options.headers && options.headers.Host) {\n      // set host value from headers.Host\n      options.host = options.headers.Host\n      // remove header property 'Host', if not removed causes net::ERR_INVALID_ARGUMENT exception\n      delete options.headers.Host\n    }\n\n    // differential downloader can call this method very often, so, better to cache session\n    if (this.cachedSession == null) {\n      this.cachedSession = getNetSession()\n    }\n\n    const request = require(\"electron\").net.request({\n      ...options,\n      session: this.cachedSession,\n    })\n    request.on(\"response\", callback)\n    if (this.proxyLoginCallback != null) {\n      request.on(\"login\", this.proxyLoginCallback)\n    }\n    return request\n  }\n\n  protected addRedirectHandlers(\n    request: ClientRequest,\n    options: RequestOptions,\n    reject: (error: Error) => void,\n    redirectCount: number,\n    handler: (options: RequestOptions) => void\n  ): void {\n    request.on(\"redirect\", (statusCode: number, method: string, redirectUrl: string) => {\n      // no way to modify request options, abort old and make a new one\n      // https://github.com/electron/electron/issues/11505\n      request.abort()\n\n      if (redirectCount > this.maxRedirects) {\n        reject(this.createMaxRedirectError())\n      } else {\n        handler(HttpExecutor.prepareRedirectUrlOptions(redirectUrl, options))\n      }\n    })\n  }\n}\n"]}