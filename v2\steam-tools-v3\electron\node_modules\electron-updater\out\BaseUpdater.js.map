{"version": 3, "file": "BaseUpdater.js", "sourceRoot": "", "sources": ["../src/BaseUpdater.ts"], "names": [], "mappings": ";;;AACA,iDAA4E;AAE5E,6CAA+D;AAE/D,MAAsB,WAAY,SAAQ,uBAAU;IAIlD,YAAsB,OAAkC,EAAE,GAAgB;QACxE,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;QAJX,yBAAoB,GAAG,KAAK,CAAA;QAC9B,qBAAgB,GAAG,KAAK,CAAA;IAIhC,CAAC;IAED,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE,eAAe,GAAG,KAAK;QACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAA;QACvD,+FAA+F;QAC/F,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;QACpG,IAAI,WAAW,EAAE,CAAC;YAChB,YAAY,CAAC,GAAG,EAAE;gBAChB,iFAAiF;gBACjF,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;gBAC9D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;YACjB,CAAC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAA;QACnC,CAAC;IACH,CAAC;IAES,eAAe,CAAC,WAAiC;QACzD,OAAO,KAAK,CAAC,eAAe,CAAC;YAC3B,GAAG,WAAW;YACd,IAAI,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAA;gBACpC,IAAI,CAAC,cAAc,EAAE,CAAA;gBACrB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;YAC1B,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;IAED,IAAc,aAAa;QACzB,OAAO,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAA;IACtF,CAAC;IAKD,wDAAwD;IACxD,OAAO,CAAC,QAAQ,GAAG,KAAK,EAAE,eAAe,GAAG,KAAK;QAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAA;YAC9E,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAA;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACxC,MAAM,kBAAkB,GAAG,sBAAsB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,CAAA;QAC5G,IAAI,aAAa,IAAI,IAAI,IAAI,kBAAkB,IAAI,IAAI,EAAE,CAAC;YACxD,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC,CAAA;YAClF,OAAO,KAAK,CAAA;QACd,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QAEhC,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,QAAQ,sBAAsB,eAAe,EAAE,CAAC,CAAA;YACxF,OAAO,IAAI,CAAC,SAAS,CAAC;gBACpB,QAAQ;gBACR,eAAe;gBACf,qBAAqB,EAAE,kBAAkB,CAAC,qBAAqB;aAChE,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACrB,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAES,cAAc;QACtB,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxD,OAAM;QACR,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAE5B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACzB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAA;gBACvF,OAAM;YACR,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAA;gBACvG,OAAM;YACR,CAAC;YAED,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uFAAuF,QAAQ,EAAE,CAAC,CAAA;gBACpH,OAAM;YACR,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;YAChD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAC3B,CAAC,CAAC,CAAA;IACJ,CAAC;IAES,QAAQ;QAChB,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAA;QACzB,MAAM,cAAc,GAAG,IAAI,IAAI,wBAAwB,CAAA;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,8DAA8D,CAAC,CAAA;QAC9F,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;YACzC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACpB,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QAC3C,CAAC;aAAM,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;QAC1C,CAAC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC1B,CAAC;IAES,YAAY,CAAC,GAAW,EAAE,OAAiB,EAAE,EAAE,GAAG,GAAG,EAAE;QAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,eAAe,IAAI,EAAE,CAAC,CAAA;QACzD,MAAM,QAAQ,GAAG,IAAA,yBAAS,EAAC,GAAG,EAAE,IAAI,EAAE;YACpC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE;YAC/B,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAA;QAEF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAA;QAClD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC1B,MAAM,KAAK,CAAA;QACb,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,GAAG,qBAAqB,MAAM,EAAE,CAAC,CAAA;QAC9D,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,EAAE,CAAA;IACtB,CAAC;IAED;;;;OAIG;IACH,oEAAoE;IACpE,4GAA4G;IAClG,KAAK,CAAC,QAAQ,CAAC,GAAW,EAAE,OAAiB,EAAE,EAAE,MAAW,SAAS,EAAE,QAAsB,QAAQ;QAC7G,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,eAAe,IAAI,EAAE,CAAC,CAAA;QACzD,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAiB,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAA;gBAC3D,MAAM,CAAC,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;gBAClC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;oBACpB,MAAM,CAAC,KAAK,CAAC,CAAA;gBACf,CAAC,CAAC,CAAA;gBACF,CAAC,CAAC,KAAK,EAAE,CAAA;gBACT,IAAI,CAAC,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,CAAA;gBACf,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAA;YACf,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAjKD,kCAiKC", "sourcesContent": ["import { AllPublishOptions } from \"builder-util-runtime\"\nimport { spawn, SpawnOptions, spawnSync, StdioOptions } from \"child_process\"\nimport { AppAdapter } from \"./AppAdapter\"\nimport { AppUpdater, DownloadExecutorTask } from \"./AppUpdater\"\n\nexport abstract class BaseUpdater extends AppUpdater {\n  protected quitAndInstallCalled = false\n  private quitHandlerAdded = false\n\n  protected constructor(options?: AllPublishOptions | null, app?: AppAdapter) {\n    super(options, app)\n  }\n\n  quitAndInstall(isSilent = false, isForceRunAfter = false): void {\n    this._logger.info(`Install on explicit quitAndInstall`)\n    // If NOT in silent mode use `autoRunAppAfterInstall` to determine whether to force run the app\n    const isInstalled = this.install(isSilent, isSilent ? isForceRunAfter : this.autoRunAppAfterInstall)\n    if (isInstalled) {\n      setImmediate(() => {\n        // this event is normally emitted when calling quitAndInstall, this emulates that\n        require(\"electron\").autoUpdater.emit(\"before-quit-for-update\")\n        this.app.quit()\n      })\n    } else {\n      this.quitAndInstallCalled = false\n    }\n  }\n\n  protected executeDownload(taskOptions: DownloadExecutorTask): Promise<Array<string>> {\n    return super.executeDownload({\n      ...taskOptions,\n      done: event => {\n        this.dispatchUpdateDownloaded(event)\n        this.addQuitHandler()\n        return Promise.resolve()\n      },\n    })\n  }\n\n  protected get installerPath(): string | null {\n    return this.downloadedUpdateHelper == null ? null : this.downloadedUpdateHelper.file\n  }\n\n  // must be sync\n  protected abstract doInstall(options: InstallOptions): boolean\n\n  // must be sync (because quit even handler is not async)\n  install(isSilent = false, isForceRunAfter = false): boolean {\n    if (this.quitAndInstallCalled) {\n      this._logger.warn(\"install call ignored: quitAndInstallCalled is set to true\")\n      return false\n    }\n\n    const downloadedUpdateHelper = this.downloadedUpdateHelper\n    const installerPath = this.installerPath\n    const downloadedFileInfo = downloadedUpdateHelper == null ? null : downloadedUpdateHelper.downloadedFileInfo\n    if (installerPath == null || downloadedFileInfo == null) {\n      this.dispatchError(new Error(\"No valid update available, can't quit and install\"))\n      return false\n    }\n\n    // prevent calling several times\n    this.quitAndInstallCalled = true\n\n    try {\n      this._logger.info(`Install: isSilent: ${isSilent}, isForceRunAfter: ${isForceRunAfter}`)\n      return this.doInstall({\n        isSilent,\n        isForceRunAfter,\n        isAdminRightsRequired: downloadedFileInfo.isAdminRightsRequired,\n      })\n    } catch (e: any) {\n      this.dispatchError(e)\n      return false\n    }\n  }\n\n  protected addQuitHandler(): void {\n    if (this.quitHandlerAdded || !this.autoInstallOnAppQuit) {\n      return\n    }\n\n    this.quitHandlerAdded = true\n\n    this.app.onQuit(exitCode => {\n      if (this.quitAndInstallCalled) {\n        this._logger.info(\"Update installer has already been triggered. Quitting application.\")\n        return\n      }\n\n      if (!this.autoInstallOnAppQuit) {\n        this._logger.info(\"Update will not be installed on quit because autoInstallOnAppQuit is set to false.\")\n        return\n      }\n\n      if (exitCode !== 0) {\n        this._logger.info(`Update will be not installed on quit because application is quitting with exit code ${exitCode}`)\n        return\n      }\n\n      this._logger.info(\"Auto install update on quit\")\n      this.install(true, false)\n    })\n  }\n\n  protected wrapSudo() {\n    const { name } = this.app\n    const installComment = `\"${name} would like to update\"`\n    const sudo = this.spawnSyncLog(\"which gksudo || which kdesudo || which pkexec || which beesu\")\n    const command = [sudo]\n    if (/kdesudo/i.test(sudo)) {\n      command.push(\"--comment\", installComment)\n      command.push(\"-c\")\n    } else if (/gksudo/i.test(sudo)) {\n      command.push(\"--message\", installComment)\n    } else if (/pkexec/i.test(sudo)) {\n      command.push(\"--disable-internal-agent\")\n    }\n    return command.join(\" \")\n  }\n\n  protected spawnSyncLog(cmd: string, args: string[] = [], env = {}): string {\n    this._logger.info(`Executing: ${cmd} with args: ${args}`)\n    const response = spawnSync(cmd, args, {\n      env: { ...process.env, ...env },\n      encoding: \"utf-8\",\n      shell: true,\n    })\n\n    const { error, status, stdout, stderr } = response\n    if (error != null) {\n      this._logger.error(stderr)\n      throw error\n    } else if (status != null && status !== 0) {\n      this._logger.error(stderr)\n      throw new Error(`Command ${cmd} exited with code ${status}`)\n    }\n\n    return stdout.trim()\n  }\n\n  /**\n   * This handles both node 8 and node 10 way of emitting error when spawning a process\n   *   - node 8: Throws the error\n   *   - node 10: Emit the error(Need to listen with on)\n   */\n  // https://github.com/electron-userland/electron-builder/issues/1129\n  // Node 8 sends errors: https://nodejs.org/dist/latest-v8.x/docs/api/errors.html#errors_common_system_errors\n  protected async spawnLog(cmd: string, args: string[] = [], env: any = undefined, stdio: StdioOptions = \"ignore\"): Promise<boolean> {\n    this._logger.info(`Executing: ${cmd} with args: ${args}`)\n    return new Promise<boolean>((resolve, reject) => {\n      try {\n        const params: SpawnOptions = { stdio, env, detached: true }\n        const p = spawn(cmd, args, params)\n        p.on(\"error\", error => {\n          reject(error)\n        })\n        p.unref()\n        if (p.pid !== undefined) {\n          resolve(true)\n        }\n      } catch (error) {\n        reject(error)\n      }\n    })\n  }\n}\n\nexport interface InstallOptions {\n  readonly isSilent: boolean\n  readonly isForceRunAfter: boolean\n  readonly isAdminRightsRequired: boolean\n}\n"]}