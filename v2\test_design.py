#!/usr/bin/env python3
"""
Simple test script to verify the new design is working
"""

import customtkinter as ctk
import sys
import os

# Add parent directory to path to import the GUI
sys.path.append(os.path.dirname(__file__))

try:
    from steam_tools_gui import ModernSteamToolsGUI
    
    def main():
        print("Testing new Steam Tools GUI design...")
        
        # Create and run the application
        app = ModernSteamToolsGUI()
        
        # Add a test message to verify logging works
        app.log_message("🎨 New design applied successfully!")
        app.log_message("✅ Steam-inspired color scheme active")
        app.log_message("🎮 Card-based layout implemented")
        app.log_message("📊 Progress percentage display added")
        
        print("GUI created successfully! Design test passed.")
        app.root.mainloop()
        
    if __name__ == "__main__":
        main()
        
except Exception as e:
    print(f"Error testing design: {e}")
    import traceback
    traceback.print_exc()
